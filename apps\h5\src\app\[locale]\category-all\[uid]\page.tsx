'use client'

import { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import {
  mergeStyles,
  resolveCatchMessage,
  type TCatchMessage,
  useGetCategoriesAllQuery,
  useToastContext,
  useVolcAnalytics,
} from '@ninebot/core'
import { useNavigate } from '@ninebot/core/src/businessHooks'
import { TRACK_EVENT } from '@ninebot/core/src/constants'
import { GetCategoriesQuery } from '@ninebot/core/src/graphql/generated/graphql'
import { Image } from 'antd-mobile'
import { SideBar } from 'antd-mobile'

import { CategoryAllSkeleton, CustomEmpty, CustomNavBar, Header } from '@/components'
import { Arrow } from '@/components/icons'
import { Link } from '@/i18n/navigation'

type Category = NonNullable<
  NonNullable<
    NonNullable<NonNullable<GetCategoriesQuery['categories']>['items']>[number]
  >['children']
>

const WINDOW_WIDTH = typeof window !== 'undefined' ? window.innerWidth : 375

export default function CategoryPage() {
  const getI18nString = useTranslations('Common')
  const toast = useToastContext()
  const { uid: decodeCategoryUid } = useParams()
  const categoryUid = decodeURIComponent(decodeCategoryUid as string)
  const { openPage } = useNavigate()
  const { reportEvent } = useVolcAnalytics()

  const {
    data: categoriesAll,
    isLoading,
    error,
  } = useGetCategoriesAllQuery(
    {
      filters: { category_uid: { eq: categoryUid as string } },
      pageSize: 9999,
      currentPage: 1,
    },
    { skip: !categoryUid },
  )
  const [catalogCategoryAll, setCatalogCategoryAll] = useState<Category>([])
  const [activeIndex, setActiveIndex] = useState<string | null | undefined>('')
  const [categoryTitle, setCategoryTitle] = useState('')
  const [showEmpty, setShowEmpty] = useState(false)

  const handleNavClick = (categoryUid: string) => {
    setActiveIndex(categoryUid)

    const categoryName = catalogCategoryAll.find((item) => item?.uid === categoryUid)?.name

    // 埋点：点击分类左侧tab
    reportEvent(TRACK_EVENT.shop_category_left_tab_click, {
      category_id: categoryUid,
      category_name: categoryName,
    })
  }

  useEffect(() => {
    if (categoriesAll?.categories?.items?.length) {
      const newCatalogCategoryAll = categoriesAll?.categories?.items?.[0]?.children?.filter(
        (item) => item?.include_in_menu === 1,
      )

      if (newCatalogCategoryAll) {
        setCategoryTitle(categoriesAll?.categories?.items?.[0]?.name || '')
        setShowEmpty(newCatalogCategoryAll?.length === 0)
        setCatalogCategoryAll(newCatalogCategoryAll)
        setActiveIndex(newCatalogCategoryAll?.[0]?.uid)
      }

      // 埋点：点击全部分类
      reportEvent(TRACK_EVENT.shop_classfication_exposure, {
        category_id: newCatalogCategoryAll?.[0]?.uid,
        category_name: newCatalogCategoryAll?.[0]?.name,
      })
    }
  }, [categoriesAll, reportEvent])

  useEffect(() => {
    if (error) {
      toast.show({
        content:
          (resolveCatchMessage(error as TCatchMessage) as string) ||
          getI18nString('fetch_data_error'),
        icon: 'fail',
      })
    }
  }, [error, toast, getI18nString])

  return (
    <div className="flex h-screen flex-col">
      <Header />
      <CustomNavBar backText={categoryTitle} customHeaderStyle="flex items-center" />
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <CategoryAllSkeleton />
        ) : (
          <>
            {showEmpty ? (
              <CustomEmpty />
            ) : (
              <div className="category-container">
                <div className="bg-gray-100">
                  <SideBar
                    style={{
                      '--width': '91px',
                    }}
                    className="category-sidebar truncate"
                    activeKey={activeIndex}
                    onChange={handleNavClick}>
                    {catalogCategoryAll.map((category) => (
                      <SideBar.Item
                        className="category-sidebar-item"
                        key={category?.uid}
                        title={category?.name}
                      />
                    ))}
                  </SideBar>
                </div>

                <div className="flex-1 overflow-y-auto">
                  {catalogCategoryAll.map((category) => (
                    <div
                      key={category?.uid}
                      className={mergeStyles(
                        'category-content',
                        activeIndex === category?.uid ? 'block' : 'hidden',
                      )}>
                      {/* Banner区域 */}
                      {category?.mobile_top_poster && (
                        <button
                          className="relative mb-[36px] w-full overflow-hidden rounded-[12px]"
                          onClick={() => {
                            openPage({ ...category.top_poster_redirect })
                          }}>
                          <Image
                            src={category?.mobile_top_poster}
                            alt={category?.name || ''}
                            className="object-cover"
                          />
                          <div className="absolute left-3 top-4 text-base leading-[22px] text-white">
                            {category.top_poster_title}
                          </div>
                        </button>
                      )}

                      {/* 二级分类列表 */}
                      {category?.children?.map((level2) => {
                        const displayMode = level2?.display_mode || 'PRODUCTS'
                        const level3Menus = level2?.children || []
                        const filterLevel3Menus = level3Menus.filter(
                          (ite) => ite?.include_in_menu === 1,
                        )

                        if (displayMode === 'PAGE') {
                          return (
                            <div key={`section-page-${level2?.uid}`}>
                              <div key={level2?.uid} className="mb-[36px]">
                                <Link
                                  href={`/${level2?.url_path}${level2?.url_suffix}`}
                                  className="mb-[8px] flex h-[32px] items-center justify-between text-[16px] leading-[22.4px]">
                                  {level2?.name}
                                  <Arrow />
                                </Link>
                                <div className="flex flex-row flex-wrap justify-between">
                                  {filterLevel3Menus.map((item) => (
                                    <Link
                                      key={item?.uid}
                                      className="flex items-center justify-center"
                                      style={{
                                        backgroundColor: '#F3F3F4',
                                        height: ((WINDOW_WIDTH - 123) / 2) * (38 / 126),
                                        width: (WINDOW_WIDTH - 123) / 2,
                                        borderRadius: 6,
                                        marginBottom: 8,
                                        padding: 12,
                                      }}
                                      href={`/${item?.url_path}${item?.url_suffix}`}
                                      onClick={() => {
                                        // 埋点：分类页产品图点击
                                        reportEvent(
                                          TRACK_EVENT.shop_category_product_picture_click,
                                          {
                                            category_id: item?.uid || '',
                                            category_name: item?.name || '',
                                          },
                                        )
                                      }}>
                                      <div className="line-clamp-1 w-full truncate text-center text-[12px] text-[#000000]">
                                        {item?.name}
                                      </div>
                                    </Link>
                                  ))}
                                </div>
                              </div>
                            </div>
                          )
                        }

                        return (
                          <div key={`section-page-${level2?.uid}`}>
                            <div key={`section-page-${level2?.url_path}${level2?.url_suffix}`}>
                              <div key={level2?.uid} className="mb-[36px]">
                                <Link
                                  href={`/${level2?.url_path}${level2?.url_suffix}`}
                                  className="mb-[8px] flex h-[32px] items-center justify-between text-[16px]"
                                  onClick={() => {
                                    // 埋点：分类顶部二级标题点击
                                    reportEvent(TRACK_EVENT.shop_category_top_tab_click, {
                                      category_id: level2?.uid || '',
                                      category_name: level2?.name || '',
                                    })
                                  }}>
                                  {level2?.name}
                                  <Arrow />
                                </Link>
                                <div className="grid grid-cols-3 gap-base-12">
                                  {filterLevel3Menus.map((item) => (
                                    <Link
                                      key={item?.uid}
                                      className="flex flex-col items-center justify-center"
                                      href={`/${item?.url_path}${item?.url_suffix}`}
                                      onClick={() => {
                                        // 埋点：分类页产品图点击
                                        reportEvent(
                                          TRACK_EVENT.shop_category_product_picture_click,
                                          {
                                            category_id: item?.uid || '',
                                            category_name: item?.name || '',
                                          },
                                        )
                                      }}>
                                      <Image
                                        src={item?.image || ''}
                                        width={79}
                                        height={79}
                                        alt={item?.name || ''}
                                        className="aspect-square object-contain"
                                        style={{ width: '100%', height: 'auto' }}
                                      />
                                      <div className="w-full truncate text-center text-[12px] leading-[1.2] text-[#000000]">
                                        {item?.name}
                                      </div>
                                    </Link>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </div>
                        )
                      })}

                      {/* Banner区域 */}
                      {category?.mobile_footer_poster && (
                        <div key={category.footer_poster_title} className="mb-8">
                          {!!category.footer_poster_redirect?.type &&
                            (!!category.footer_poster_redirect?.url ||
                              !!category.footer_poster_redirect?.value) && (
                              <button
                                className="mb-4 flex items-center justify-between font-miSansDemiBold450 text-[16px] leading-[22.4px]"
                                onClick={() => {
                                  openPage({ ...category.footer_poster_redirect })
                                }}>
                                {category.footer_poster_title}
                                <Arrow />
                              </button>
                            )}

                          <div className="relative mb-6 w-full overflow-hidden rounded-[12px]">
                            <Image
                              src={category.mobile_footer_poster}
                              alt={category?.name || ''}
                              className="!h-[80px] object-fill"
                            />
                            <div className="absolute left-3 top-4 text-base leading-[22px] text-white">
                              {category.footer_poster_subtitle}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}
